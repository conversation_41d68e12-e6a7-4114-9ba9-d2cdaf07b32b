import dayjs from 'dayjs'
import { getDepartmentPermissionTree } from '/@/api/common/dept'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { formateerNotCurrency } from '/@/utils/ERP/formatterPrice'
import { isNullOrUnDef } from '/@/utils/is'
import { h, ref } from 'vue'
import { getFeeStaticDetail } from '/@/api/order/feeStatic'
import { useGo } from '/@/hooks/web/usePage'
import { router } from '/@/router'
import { useProfitPotentialStore } from '/@/store/modules/profitPotential'

export const FEESTATICPATH = '/reportForm/feeStatic'
export const FEESTATICCATEGORYPATH = '/reportForm/feeStaticCategory'
export const FEESTATICDEPTPATH = '/reportForm/feeStaticDept'

export const searchInfo = ref({})
export const searchInfoCategory = ref({})
export const searchInfoDept = ref({})

const profitPotentialStore = useProfitPotentialStore()
const go = useGo(router)
// 创建辅助函数生成月份列配置
// 定义点击行为的类型
interface ClickHandler {
  type: 'modal' | 'navigate' | 'none'
  handler?: (record: any, month: number) => void | Promise<void>
  modalProps?: { setModalProps: Function; openModal: Function }
  navigateProps?: { router: any }
}

// 使用示例：
// 1. Modal 点击：
// createMonthColumn(1, path, { type: 'modal', modalProps: { setModalProps, openModal } })
//
// 2. 页面跳转：
// createMonthColumn(1, path, { type: 'navigate', navigateProps: { router } })
//
// 3. 自定义处理：
// createMonthColumn(1, path, { type: 'modal', handler: async (record, month) => { /* 自定义逻辑 */ } })
//
// 4. 无点击事件：
// createMonthColumn(1, path, { type: 'none' }) 或 createMonthColumn(1, path)

const createMonthColumn = (month: number, path: string, clickHandler?: ClickHandler): BasicColumn => {
  // 格式化月份为两位数
  const formattedMonth = month.toString().padStart(2, '0')
  // 是否为第一个月份（1月），用于确定是否添加customRender
  const isFirstMonth = month === 1

  return {
    title: `${month}月`,
    dataIndex: `${month}`,
    width: 240,
    resizable: true,
    children: [
      {
        title: '费用总计',
        dataIndex: formattedMonth,
        width: isFirstMonth ? 120 : undefined,
        resizable: isFirstMonth ? true : undefined,
        customRender: ({ text, record }) => {
          // 如果没有点击处理器或类型为none，则不显示为可点击
          if (!clickHandler || clickHandler.type === 'none') {
            return h('div', {}, !isNullOrUnDef(text) ? formateerNotCurrency.format(text) : '-')
          }

          return h(
            'div',
            {
              style: {
                cursor: 'pointer',
                color: '#1890ff'
              },
              onClick: async () => {
                if (clickHandler.type === 'modal' && clickHandler.modalProps) {
                  // Modal 处理逻辑
                  const { setModalProps, openModal } = clickHandler.modalProps
                  // 获取当前的searchInfo值，而不是直接引用
                  let currentSearchInfo: any = {}
                  if (path === FEESTATICPATH) {
                    currentSearchInfo = { ...searchInfo.value }
                  } else if (path === FEESTATICCATEGORYPATH) {
                    currentSearchInfo = { ...searchInfoCategory.value }
                  }
                  const { items } = await getFeeStaticDetail({
                    ...currentSearchInfo,
                    category_sub: record.category,
                    month
                  })
                  const columns = monthColumns(path, month)
                  setModalProps({
                    title: record.category
                  })
                  openModal(true, {
                    details: items,
                    columns
                  })
                } else if (clickHandler.type === 'navigate' && clickHandler.navigateProps) {
                  // 页面跳转处理逻辑
                  // 这里可以根据需要构建跳转参数
                  console.log(record)

                  console.log(searchInfoDept.value)
                  console.log(month, path)

                  const transferData = {
                    department: record.dept_id,
                    ...searchInfoDept.value,
                    group_by: 'category'
                  }
                  profitPotentialStore.setTofeestaticParams(transferData)

                  go('/reportForm/feeStatic')
                } else if (clickHandler.handler) {
                  // 自定义处理函数
                  await clickHandler.handler(record, month)
                }
              }
            },
            !isNullOrUnDef(text) ? formateerNotCurrency.format(text) : '-'
          )
        }
      },
      {
        title: '费用占比',
        dataIndex: `${formattedMonth} percent`,
        width: isFirstMonth ? 120 : undefined,
        resizable: isFirstMonth ? true : undefined
      }
    ]
  }
}

//不需要点击事件的月份配置
const createMonthColumnNoClick = (month: number): BasicColumn => {
  // 格式化月份为两位数
  const formattedMonth = month.toString().padStart(2, '0')
  // 是否为第一个月份（1月），用于确定是否添加customRender
  const isFirstMonth = month === 1

  return {
    title: `${month}月`,
    dataIndex: `${month}`,
    width: 240,
    resizable: true,
    children: [
      {
        title: '费用总计',
        dataIndex: formattedMonth,
        width: isFirstMonth ? 120 : undefined,
        resizable: isFirstMonth ? true : undefined,
        customRender: ({ text }) => {
          return h('div', {}, !isNullOrUnDef(text) ? formateerNotCurrency.format(text) : '-')
        }
      },
      {
        title: '费用占比',
        dataIndex: `${formattedMonth} percent`,
        width: isFirstMonth ? 120 : undefined,
        resizable: isFirstMonth ? true : undefined
      }
    ]
  }
}

export const getColumns = (path: string): BasicColumn[] => [
  {
    title: '科目名称',
    dataIndex: 'category',
    width: 250,
    resizable: true,
    ifShow: [FEESTATICPATH, FEESTATICCATEGORYPATH].includes(path),
    customRender: ({ text }) => {
      return h(
        'div',
        {
          // style: {
          //   cursor: 'pointer',
          //   color: '#1890ff'
          // },
          // onClick: async () => {
          //   // 获取当前的searchInfo值，而不是直接引用
          //   let currentSearchInfo: any = {}
          //   if (path === FEESTATICPATH) {
          //     currentSearchInfo = { ...searchInfo.value }
          //   } else if (path === FEESTATICCATEGORYPATH) {
          //     currentSearchInfo = { ...searchInfoCategory.value }
          //   }
          //   const { items } = await getFeeStaticDetail({
          //     ...currentSearchInfo,
          //     category_sub: record.category2l
          //   })
          //   const columns = categoryColumns(path)
          //   setModalProps({
          //     title: record.category2l
          //   })
          //   openModal(true, {
          //     details: items,
          //     columns
          //   })
          // }
        },
        text
      )
    }
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 120,
    resizable: true,
    ifShow: [FEESTATICDEPTPATH].includes(path)
  },
  // 使用Array.from生成12个月份的列配置
  ...Array.from({ length: 12 }, (_, i) =>
    createMonthColumn(i + 1, path, {
      type: 'none'
    })
  ),
  {
    title: '本年累计费用',
    dataIndex: 'total',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return !isNullOrUnDef(value) ? formateerNotCurrency.format(Number(value)) : '-'
    }
  },
  {
    title: '累计费用占比 %',
    dataIndex: 'total percent',
    width: 120,
    resizable: true
  }
]

export const schema: FormSchema[] = [
  {
    field: 'year',
    label: '年',
    component: 'DatePicker',
    required: true,
    defaultValue: dayjs().format('YYYY'),
    componentProps: {
      format: 'YYYY',
      picker: 'year',
      style: {
        width: '100%'
      }
    }
  },
  {
    field: 'department',
    label: `部门`,
    component: 'ApiTreeSelect',
    colProps: { span: 24 },
    componentProps: ({}) => ({
      resultField: 'items',
      immediate: false,
      lazyLoad: true,
      api: getDepartmentPermissionTree,
      treeSelectProps: {
        treeDataSimpleMode: true,
        fieldNames: { children: 'children', key: 'key', value: 'name', label: 'name' },
        // treeCheckable: true,
        // showCheckedStrategy: 'SHOW_ALL',
        treeDefaultExpandAll: true,
        showSearch: true,
        treeLine: {
          showLeafIcon: false
        },

        filterTreeNode: (search: string, item: any) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    })
  },
  {
    field: 'category_sub',
    label: '子科目',
    component: 'Input'
  }
]

export const categoryColumns = (path: string): BasicColumn[] => [
  {
    title: '科目名称',
    dataIndex: 'category',
    width: 200,
    resizable: true,
    ifShow: [FEESTATICPATH, FEESTATICCATEGORYPATH].includes(path)
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 120,
    resizable: true,
    ifShow: [FEESTATICDEPTPATH].includes(path)
  },
  // 使用Array.from生成12个月份的列配置
  ...Array.from({ length: 12 }, (_, i) =>
    createMonthColumn(i + 1, path, {
      type: 'navigate',
      navigateProps: { router }
    })
  ),
  {
    title: '本年累计费用',
    dataIndex: 'total',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return !isNullOrUnDef(value) ? formateerNotCurrency.format(Number(value)) : '-'
    }
  },
  {
    title: '累计费用占比 %',
    dataIndex: 'total percent',
    width: 120,
    resizable: true
  }
]

const monthColumns = (path: string, month: number): BasicColumn[] => [
  {
    title: '科目名称',
    dataIndex: 'category',
    width: 200,
    resizable: true,
    ifShow: [FEESTATICPATH, FEESTATICCATEGORYPATH].includes(path)
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 120,
    resizable: true,
    ifShow: [FEESTATICDEPTPATH].includes(path)
  },
  // 使用Array.from生成12个月份的列配置
  Array.from({ length: 12 }, (_, i) => createMonthColumnNoClick(i + 1)).find((item) => item.dataIndex === String(month))!,
  {
    title: '本年累计费用',
    dataIndex: 'total',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return !isNullOrUnDef(value) ? formateerNotCurrency.format(Number(value)) : '-'
    }
  },
  {
    title: '累计费用占比 %',
    dataIndex: 'total percent',
    width: 120,
    resizable: true
  }
]
