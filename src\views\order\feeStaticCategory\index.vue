<template>
  <div>
    <BasicTable @register="registerTable" :loading="loading" />
    <!-- <DetailsModal @register="registerModal" /> -->
  </div>
</template>
<script lang="ts" setup name="PAGE_303">
import { useTable, BasicTable } from '/@/components/Table'
import { getColumns, schema, searchInfoCategory } from '../feeStatic/datas/data'
import dayjs from 'dayjs'
import { getFeeStatic } from '/@/api/order/feeStatic'
import { nextTick, onMounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
// import { useModal } from '/@/components/Modal'
// import DetailsModal from '../feeStatic/components/DetailsModal.vue'
import { storeToRefs } from 'pinia'
import { useProfitPotentialStore } from '/@/store/modules/profitPotential'
import { cloneDeep } from 'lodash-es'
const route = useRoute()

// const [registerModal, { openModal, setModalProps }] = useModal()
const loading = ref(true)
const profitPotentialStore = useProfitPotentialStore()

const { tofeestaticParams } = storeToRefs(profitPotentialStore)

watch(
  () => tofeestaticParams.value,
  (newVal: any) => {
    if (newVal) {
      console.log(newVal, '--------------')

      nextTick(async () => {
        const formInstance = await getForm() // 确保在异步回调中获取表单实例
        if (formInstance) {
          const { group_by, department, category1 } = newVal
          console.log(group_by, department, category1, '-----------------')

          const newFormData = { group_by, department, category1 }
          await getForm().setFieldsValue(newFormData)
          setTimeout(async () => {
            const params = await handleBeforeFetch(newFormData)

            reload(params)
          })
        }
      })
    }
  },
  {
    immediate: true,
    deep: true
  }
)

const [registerTable, { setColumns, reload, getForm }] = useTable({
  searchInfo: {
    category1: '营业费用',
    group_by: 'category'
  },
  title: '费用汇总(按科目)',
  api: getFeeStatic,
  showTableSetting: true,
  columns: [],
  rowKey: 'id',
  size: 'small',
  bordered: true,
  showIndexColumn: false,
  pagination: false,
  useSearchForm: true,
  immediate: false,
  formConfig: {
    labelWidth: 150,
    schemas: cloneDeep(schema),
    autoSubmitOnEnter: true,
    baseColProps: { span: 8 },
    fieldMapToTime: [
      ['submited_at', ['submited_at_start', 'submited_at_end'], ['YYYY-MM-DD', 'YYYY-MM-DD']],
      ['audit_at', ['audit_at_start', 'audit_at_end'], ['YYYY-MM-DD', 'YYYY-MM-DD']]
    ]
  },
  beforeFetch: handleBeforeFetch
})

function handleBeforeFetch(params) {
  searchInfoCategory.value = {
    ...params,
    year: dayjs(params.year).format('YYYY')
  }
  return searchInfoCategory.value
}

onMounted(async () => {
  // 初始化表格
  await setColumns(getColumns(route.path))

  reload()
})
</script>
