<template>
  <div>
    <BasicTable @register="registerTable" :search-info="{ category1: '营业费用', group_by: 'department' }" />
  </div>
</template>
<script lang="ts" setup name="PAGE_304">
import { useTable, BasicTable } from '/@/components/Table'
import { categoryColumns, schema, searchInfoDept } from '../feeStatic/datas/data'
import dayjs from 'dayjs'
import { getFeeStaticDetail } from '/@/api/order/feeStatic'
import { useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import { watch, nextTick } from 'vue'
import { useProfitPotentialStore } from '/@/store/modules/profitPotential'
import { cloneDeep } from 'lodash-es'

const profitPotentialStore = useProfitPotentialStore()
const { toProductOperationParams } = storeToRefs(profitPotentialStore)

watch(
  () => toProductOperationParams.value,
  (newVal: any) => {
    if (newVal) {
      console.log(newVal)

      nextTick(async () => {
        const formInstance = await getForm() // 确保在异步回调中获取表单实例
        if (formInstance) {
          const newFormData = {
            category1: '营业费用',
            group_by: 'department',
            department: newVal.department[0].label,
            year: newVal.endDate.split('-')[0]
          }
          await getForm().setFieldsValue(newFormData)
          setTimeout(async () => {
            const params = await handleBeforeFetch(newFormData)

            reload(params)
          })
        }
      })
    }
  },
  {
    immediate: true,
    deep: true
  }
)

const route = useRoute()

const [registerTable, { reload, getForm }] = useTable({
  api: getFeeStaticDetail,
  showTableSetting: true,
  columns: categoryColumns(route.path),
  rowKey: 'id',
  size: 'small',
  bordered: true,
  showIndexColumn: false,
  pagination: false,
  useSearchForm: true,
  searchInfo: {
    category1: '营业费用',
    group_by: 'department'
  },
  title: '费用汇总(按部门)',
  formConfig: {
    labelWidth: 150,
    schemas: cloneDeep(schema),
    autoSubmitOnEnter: true,
    baseColProps: { span: 8 },
    fieldMapToTime: [
      ['submited_at', ['submited_at_start', 'submited_at_end'], ['YYYY-MM-DD', 'YYYY-MM-DD']],
      ['audit_at', ['audit_at_start', 'audit_at_end'], ['YYYY-MM-DD', 'YYYY-MM-DD']]
    ]
  },
  beforeFetch: handleBeforeFetch
})

function handleBeforeFetch(params) {
  searchInfoDept.value = {
    ...params,
    year: dayjs(params.year).format('YYYY')
  }
  return searchInfoDept.value
}
</script>
